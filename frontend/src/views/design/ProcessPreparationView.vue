<template>
  <div class="process-preparation-container">
    <div class="process-header">
      <div class="header-left">
        <button class="add-btn" @click="openMaterialUsage" :disabled="!canEditProcess">
          <i class="fas fa-plus"></i> 材料用量表
        </button>
        <h2>工序列表</h2>
      </div>

      <div class="header-center">
        <!-- 下一步控制組件 -->
        <NextStepControl
          :history-manager="historyManager"
          :current-state="getCurrentEditState()"
          @state-changed="handleHistoryStateChanged"
          @clear-history="handleClearHistory"
        />
      </div>

      <div class="header-right">
        <button class="multi-select-btn" @click="toggleMultiSelectMode" v-if="canEditProcess">
          <i class="fas" :class="isMultiSelectMode ? 'fa-times' : 'fa-check-square'" />
          {{ isMultiSelectMode ? '取消多選' : '多選' }}
        </button>
        <button class="clear-btn" @click="clearAllProcessesWrapper" v-if="savedProcesses.length > 0">
          <i class="fas fa-trash"></i> 清除所有工序
        </button>
        <div class="back-to-bom" @click="backToBom">
          <i class="fas fa-arrow-left"></i>
          返回BOM
        </div>
      </div>
    </div>
    <div v-if="isMultiSelectMode" class="multi-select-actions">
      <button class="batch-add-btn" :disabled="multiSelectedMaterials.length === 0" @click="openBatchAddModal">
        <i class="fas fa-plus"></i> 批量新增工序
      </button>
      <button class="batch-delete-btn" :disabled="multiSelectedProcesses.length === 0" @click="openBatchDeleteModal">
        <i class="fas fa-trash"></i> 批量刪除工序
      </button>
      <span class="multi-select-count">
        已選擇 {{ multiSelectedMaterials.length }} 項材料、{{ multiSelectedProcesses.length }} 項工序
      </span>
    </div>
    <div v-if="filteredMaterial.length > 0" class="material-filter-bar">
      <span>目前僅顯示：
        <template v-for="(mat, idx) in filteredMaterial" :key="mat">
          <b>{{ mat }}</b>
          <button class="remove-filter-btn" @click="removeMaterialFromFilter(mat)" title="移除此材料">×</button>
          <span v-if="idx !== filteredMaterial.length - 1">、</span>
        </template>
        的所有工序
      </span>
      <button class="clear-filter-btn" @click="clearMaterialFilter">全部清除</button>
    </div>

    <div v-if="!canEditProcess" class="read-only-notice">
      <p v-if="currentUserDepartment === 0">您所屬的部門僅可查看備料工序資料，無法進行編輯操作。</p>
      <p v-else-if="Number(bomConfirmationStatus) !== 0">此BOM已被確認，無法進行編輯操作。只有未確認狀態的BOM才能進行編輯。</p>
    </div>

    <!-- 流程圖區域 -->
    <div class="process-flow-container">
      <!-- 大工序標題列 -->
      <div class="process-flow-header">
        <!-- 材料標題列 -->
        <div class="process-column material-column">
          <h3>材料區</h3>
        </div>

        <!-- 大工序標題列 -->
        <div class="process-column" v-for="(process, index) in majorProcesses" :key="index">
          <h3>{{ process.charAt(0) }}<span>{{ process.slice(1) }}</span></h3>
        </div>
      </div>

      <!-- 流程圖主體 -->
      <div class="process-flow-body">
        <!-- 部位名稱導入提示 -->
        <div class="position-import-hint" v-if="positionNames.length === 0">
          <p>部位名稱尚未導入</p>
          <button class="import-btn" @click="openMaterialUsage">
            <i class="fas fa-file-import"></i> 從材料用量表導入部位名稱
          </button>
        </div>

        <!-- 流程圖內容 -->
        <div class="process-flow-content" v-else>
          <!-- 連接線容器 -->
          <div class="connection-lines" v-if="hasFixedProcesses || hoveredProcess">
            <svg class="connection-svg" :width="connectionSvgWidth" :height="connectionSvgHeight">
              <!-- 定義箭頭標記 -->
              <defs>
                <marker
                  id="arrowhead"
                  markerWidth="5"
                  markerHeight="3.5"
                  refX="4.5"
                  refY="1.75"
                  orient="auto"
                >
                  <polygon
                    points="0 0, 5 1.75, 0 3.5"
                    fill="#b0b0b0"
                  />
                </marker>
              </defs>
              
              <g v-for="(connection, index) in processConnections" :key="index">
                <!-- 連接線 -->
                <path 
                  :d="connection.pathData"
                  stroke="#b0b0b0" 
                  stroke-width="3" 
                  fill="none"
                  class="connection-line"
                  marker-end="url(#arrowhead)"
                />
              </g>
            </svg>
          </div>

          <!-- 材料列（可折疊） -->
          <div class="process-column material-column">
            <div class="material-collapse-header" @click="materialCollapsed = !materialCollapsed">
              <span>{{ materialCollapsed ? '展開材料區' : '收合材料區' }}</span>
              <i :class="materialCollapsed ? 'fas fa-chevron-down' : 'fas fa-chevron-up'" style="margin-left: 8px;"></i>
            </div>
            <transition name="fade">
              <div class="position-items" v-show="!materialCollapsed">
                <template v-for="(group, groupIdx) in groupedMaterials" :key="'material-group-' + groupIdx">
                  <!-- 合併後的材料組 -->
                  <div v-if="group.type === 'merged'" class="material-group merged-group">
                    <div
                      class="material-group-header"
                      @click="handleMergedMaterialClick(group.displayName, $event)"
                      @contextmenu.prevent="handleMergedMaterialClick(group.displayName, $event)"
                      :class="{
                        'highlighted': isMaterialHighlighted(group.displayName)
                      }"
                      @mouseenter="hoveredMaterial = group.displayName"
                      @mouseleave="hoveredMaterial = null"
                    >
                      <div class="group-name">
                        <i
                          :class="materialGroupCollapsed[group.displayName] ? 'fas fa-chevron-right' : 'fas fa-chevron-down'"
                          class="collapse-icon"
                          @click.stop="materialGroupCollapsed[group.displayName] = !materialGroupCollapsed[group.displayName]"
                        ></i>
                        {{ group.displayName }}
                        <span class="merged-indicator">(已合併)</span>
                      </div>
                    </div>

                    <!-- 原始材料列表（可摺疊） -->
                    <transition name="fade">
                      <div v-show="!materialGroupCollapsed[group.displayName]" class="original-materials-list">
                        <div
                          v-for="(originalMaterial, idx) in group.originalMaterials"
                          :key="'original-' + idx"
                          class="position-item original-material merged"
                          :style="{ backgroundColor: materialColorsForList[positionNames.indexOf(originalMaterial)] || '#e5e7eb', color: '#666', fontWeight: 400 }"
                        >
                          {{ originalMaterial }}
                          <span class="material-merged-indicator">已合併</span>
                        </div>
                      </div>
                    </transition>
                  </div>

                  <!-- 原始材料 -->
                  <div v-else class="position-item"
                    :class="{
                      'selected': false,
                      'multi-selecting': isMultiSelectMode,
                      'multi-selected': isMultiSelectMode && multiSelectedMaterials.includes(group.displayName),
                      'disabled': isMultiSelectMode && isMaterialUsed(group.displayName),
                      'highlighted': isMaterialHighlighted(group.displayName)
                    }"
                    @click="isMultiSelectMode && isMaterialUsed(group.displayName) ? null : handleMaterialClick(group.displayName, $event)"
                    @mouseenter="hoveredMaterial = group.displayName"
                    @mouseleave="hoveredMaterial = null"
                    :style="{ backgroundColor: materialColorsForList[positionNames.indexOf(group.displayName)] || '#e5e7eb', color: '#222', fontWeight: 500, display: 'flex', alignItems: 'center' }"
                  >
                    <template v-if="isMultiSelectMode">
                      <input type="checkbox" class="multi-checkbox" :checked="multiSelectedMaterials.includes(group.displayName)" @change="handleMultiSelectMaterial(group.displayName, $event)" @click.stop :disabled="isMaterialUsed(group.displayName)" style="position: static; margin-right: 8px; margin-left: 0;" />
                    </template>
                    {{ group.displayName }}
                    <span v-if="isMaterialUsed(group.displayName)" class="material-has-process"></span>
                  </div>
                </template>
              </div>
            </transition>
          </div>

          <!-- 大工序A列 -->
          <div class="process-column">
            <div class="position-items">
              <div
                v-for="process in getProcessesByMajorProcess('A選料')"
                :key="process.id"
                class="process-item"
                :data-source="process.sourcePosition"
                :data-id="process.id"
                @mouseenter="hoveredProcess = process.id"
                @mouseleave="hoveredProcess = null"
                :class="{
                  'completed': process.isCompleted,
                  'fixed': process.isFixed,
                  'highlighted': isProcessHighlighted(process),
                  'dimmed': shouldDimProcessByProcess(process),
                  'multi-selecting': isMultiSelectMode,
                  'multi-selected': isMultiSelectMode && multiSelectedProcesses.includes(process.id)
                }"
                @click="isMultiSelectMode ? handleMultiSelectProcess(process.id, $event) : showProcessMenuWrapper(process, $event)"
              >
                <template v-if="isMultiSelectMode">
                  <input type="checkbox" class="multi-checkbox" :checked="multiSelectedProcesses.includes(process.id)" @change="handleMultiSelectProcess(process.id, $event)" @click.stop style="position: static; margin-right: 8px; margin-left: 0;" />
                </template>
                <div class="process-item-order" :style="{ backgroundColor: getMaterialColorByName(process.pieceName) }">{{ getProcessIndex(process, 'A選料') }}</div>
                <div class="process-item-name">{{ process.pieceName }}</div>
                <div class="process-item-minor">{{ getMinorProcessName(process) }}</div>
              </div>
            </div>
          </div>

          <!-- 大工序B列 -->
          <div class="process-column">
            <div class="position-items">
              <div
                v-for="process in getProcessesByMajorProcess('B開料')"
                :key="process.id"
                class="process-item"
                :data-source="process.sourcePosition"
                :data-id="process.id"
                @mouseenter="hoveredProcess = process.id"
                @mouseleave="hoveredProcess = null"
                :class="{
                  'completed': process.isCompleted,
                  'fixed': process.isFixed,
                  'highlighted': isProcessHighlighted(process),
                  'dimmed': shouldDimProcessByProcess(process),
                  'multi-selecting': isMultiSelectMode,
                  'multi-selected': isMultiSelectMode && multiSelectedProcesses.includes(process.id)
                }"
                @click="isMultiSelectMode ? handleMultiSelectProcess(process.id, $event) : showProcessMenuWrapper(process, $event)"
              >
                <template v-if="isMultiSelectMode">
                  <input type="checkbox" class="multi-checkbox" :checked="multiSelectedProcesses.includes(process.id)" @change="handleMultiSelectProcess(process.id, $event)" @click.stop style="position: static; margin-right: 8px; margin-left: 0;" />
                </template>
                <div class="process-item-order" :style="{ backgroundColor: getMaterialColorByName(process.pieceName) }">{{ getProcessIndex(process, 'B開料') }}</div>
                <div class="process-item-name">{{ process.pieceName }}</div>
                <div class="process-item-minor">{{ getMinorProcessName(process) }}</div>
              </div>
            </div>
          </div>

          <!-- 大工序C列 -->
          <div class="process-column">
            <div class="position-items">
              <div
                v-for="process in getProcessesByMajorProcess('C備料')"
                :key="process.id"
                class="process-item"
                :data-source="process.sourcePosition"
                :data-id="process.id"
                @mouseenter="hoveredProcess = process.id"
                @mouseleave="hoveredProcess = null"
                :class="{
                  'completed': process.isCompleted,
                  'fixed': process.isFixed,
                  'highlighted': isProcessHighlighted(process),
                  'dimmed': shouldDimProcessByProcess(process),
                  'multi-selecting': isMultiSelectMode,
                  'multi-selected': isMultiSelectMode && multiSelectedProcesses.includes(process.id)
                }"
                @click="isMultiSelectMode ? handleMultiSelectProcess(process.id, $event) : showProcessMenuWrapper(process, $event)"
              >
                <template v-if="isMultiSelectMode">
                  <input type="checkbox" class="multi-checkbox" :checked="multiSelectedProcesses.includes(process.id)" @change="handleMultiSelectProcess(process.id, $event)" @click.stop style="position: static; margin-right: 8px; margin-left: 0;" />
                </template>
                <div class="process-item-order" :style="{ backgroundColor: getMaterialColorByName(process.pieceName) }">{{ getProcessIndex(process, 'C備料') }}</div>
                <div class="process-item-name">{{ process.pieceName }}</div>
                <div class="process-item-minor">{{ getMinorProcessName(process) }}</div>
              </div>
            </div>
          </div>

          <!-- 大工序D列 -->
          <div class="process-column">
            <div class="position-items">
              <div
                v-for="process in getProcessesByMajorProcess('D塗邊')"
                :key="process.id"
                class="process-item"
                :data-source="process.sourcePosition"
                :data-id="process.id"
                @mouseenter="hoveredProcess = process.id"
                @mouseleave="hoveredProcess = null"
                :class="{
                  'completed': process.isCompleted,
                  'fixed': process.isFixed,
                  'highlighted': isProcessHighlighted(process),
                  'dimmed': shouldDimProcessByProcess(process),
                  'multi-selecting': isMultiSelectMode,
                  'multi-selected': isMultiSelectMode && multiSelectedProcesses.includes(process.id)
                }"
                @click="isMultiSelectMode ? handleMultiSelectProcess(process.id, $event) : showProcessMenuWrapper(process, $event)"
              >
                <template v-if="isMultiSelectMode">
                  <input type="checkbox" class="multi-checkbox" :checked="multiSelectedProcesses.includes(process.id)" @change="handleMultiSelectProcess(process.id, $event)" @click.stop style="position: static; margin-right: 8px; margin-left: 0;" />
                </template>
                <div class="process-item-order" :style="{ backgroundColor: getMaterialColorByName(process.pieceName) }">{{ getProcessIndex(process, 'D塗邊') }}</div>
                <div class="process-item-name">{{ process.pieceName }}</div>
                <div class="process-item-minor">{{ getMinorProcessName(process) }}</div>
              </div>
            </div>
          </div>

          <!-- 大工序E列 -->
          <div class="process-column">
            <div class="position-items">
              <div
                v-for="process in getProcessesByMajorProcess('E製作')"
                :key="process.id"
                class="process-item"
                :data-source="process.sourcePosition"
                :data-id="process.id"
                @mouseenter="hoveredProcess = process.id"
                @mouseleave="hoveredProcess = null"
                :class="{
                  'completed': process.isCompleted,
                  'fixed': process.isFixed,
                  'highlighted': isProcessHighlighted(process),
                  'dimmed': shouldDimProcessByProcess(process),
                  'multi-selecting': isMultiSelectMode,
                  'multi-selected': isMultiSelectMode && multiSelectedProcesses.includes(process.id)
                }"
                @click="isMultiSelectMode ? handleMultiSelectProcess(process.id, $event) : showProcessMenuWrapper(process, $event)"
              >
                <template v-if="isMultiSelectMode">
                  <input type="checkbox" class="multi-checkbox" :checked="multiSelectedProcesses.includes(process.id)" @change="handleMultiSelectProcess(process.id, $event)" @click.stop style="position: static; margin-right: 8px; margin-left: 0;" />
                </template>
                <div class="process-item-order" :style="{ backgroundColor: getMaterialColorByName(process.pieceName) }">{{ getProcessIndex(process, 'E製作') }}</div>
                <div class="process-item-name">{{ process.pieceName }}</div>
                <div class="process-item-minor">{{ getMinorProcessName(process) }}</div>
              </div>
            </div>
          </div>

          <!-- 大工序F列 -->
          <div class="process-column">
            <div class="position-items">
              <div
                v-for="process in getProcessesByMajorProcess('F包裝')"
                :key="process.id"
                class="process-item"
                :data-source="process.sourcePosition"
                :data-id="process.id"
                @mouseenter="hoveredProcess = process.id"
                @mouseleave="hoveredProcess = null"
                :class="{
                  'completed': process.isCompleted,
                  'fixed': process.isFixed,
                  'highlighted': isProcessHighlighted(process),
                  'dimmed': shouldDimProcessByProcess(process),
                  'multi-selecting': isMultiSelectMode,
                  'multi-selected': isMultiSelectMode && multiSelectedProcesses.includes(process.id)
                }"
                @click="isMultiSelectMode ? handleMultiSelectProcess(process.id, $event) : showProcessMenuWrapper(process, $event)"
              >
                <template v-if="isMultiSelectMode">
                  <input type="checkbox" class="multi-checkbox" :checked="multiSelectedProcesses.includes(process.id)" @change="handleMultiSelectProcess(process.id, $event)" @click.stop style="position: static; margin-right: 8px; margin-left: 0;" />
                </template>
                <div class="process-item-order" :style="{ backgroundColor: getMaterialColorByName(process.pieceName) }">{{ getProcessIndex(process, 'F包裝') }}</div>
                <div class="process-item-name">{{ process.pieceName }}</div>
                <div class="process-item-minor">{{ getMinorProcessName(process) }}</div>
              </div>
            </div>
          </div>

          <!-- 大工序G列 -->
          <div class="process-column">
            <div class="position-items">
              <div
                v-for="process in getProcessesByMajorProcess('G補料')"
                :key="process.id"
                class="process-item"
                :data-source="process.sourcePosition"
                :data-id="process.id"
                @mouseenter="hoveredProcess = process.id"
                @mouseleave="hoveredProcess = null"
                :class="{
                  'completed': process.isCompleted,
                  'fixed': process.isFixed,
                  'highlighted': isProcessHighlighted(process),
                  'dimmed': shouldDimProcessByProcess(process),
                  'multi-selecting': isMultiSelectMode,
                  'multi-selected': isMultiSelectMode && multiSelectedProcesses.includes(process.id)
                }"
                @click="isMultiSelectMode ? handleMultiSelectProcess(process.id, $event) : showProcessMenuWrapper(process, $event)"
              >
                <template v-if="isMultiSelectMode">
                  <input type="checkbox" class="multi-checkbox" :checked="multiSelectedProcesses.includes(process.id)" @change="handleMultiSelectProcess(process.id, $event)" @click.stop style="position: static; margin-right: 8px; margin-left: 0;" />
                </template>
                <div class="process-item-order" :style="{ backgroundColor: getMaterialColorByName(process.pieceName) }">{{ getProcessIndex(process, 'G補料') }}</div>
                <div class="process-item-name">{{ process.pieceName }}</div>
                <div class="process-item-minor">{{ getMinorProcessName(process) }}</div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>

    <!-- 彈出材料用量表 -->
    <MaterialUsageView
      :visible="materialUsageVisible"
      :bomId="bomId"
      @close="closeMaterialUsage"
      @material-selected="handleMaterialSelected"
    />

    <!-- 工序選擇模態對話框 -->
    <div class="process-modal" v-if="processModalVisible">
      <div class="process-modal-content">
        <div class="process-modal-header">
          <h3>工序選擇 - {{ selectedPosition }}</h3>
          <button class="close-btn" @click="closeProcessModal">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="process-modal-body">



          <!-- 分片名稱 -->
          <div class="form-group">
            <label>分片名稱：<span class="required">*</span></label>
            <input type="text" v-model="processForm.pieceName" placeholder="請輸入分片名稱" />
          </div>

          <!-- 部位組織 -->
          <div class="form-group">
            <label>部位組織：</label>
            <select v-model="processForm.partStructure" @change="handlePartStructureChangeWrapper">
              <option value="">請選擇部位組織</option>
              <option v-for="(part, index) in partStructureList" :key="index" :value="part">
                {{ part.code }} {{ part.name }}
              </option>
            </select>
          </div>

          <!-- 分片組織 -->
          <div class="form-group">
            <label>分片組織：</label>
            <select v-model="processForm.pieceStructure">
              <option value="">請選擇分片組織</option>
              <option v-for="(piece, index) in filteredPieceStructures" :key="index" :value="piece">
                {{ piece.code }} {{ piece.name }}
              </option>
            </select>
          </div>

          <!-- 材料 -->
          <div class="form-group">
            <label>材料：</label>
            <select v-model="processForm.material">
              <option value="">請選擇材料</option>
              <option v-for="(material, index) in materialList" :key="index" :value="material">
                {{ material.name }}
              </option>
            </select>
          </div>

          <!-- 大工序 -->
          <div class="form-group">
            <label>大工序：</label>
            <select v-model="processForm.majorProcess" @change="handleMajorProcessChangeWrapper">
              <option value="">請選擇大工序</option>
              <option v-for="(process, index) in filteredMajorProcesses" :key="index" :value="process">
                {{ process }}
              </option>
            </select>
          </div>

          <!-- 小工序 -->
          <div class="form-group">
            <label>小工序：</label>
            <select v-model="processForm.minorProcess" @change="handleMinorProcessChangeWrapper">
              <option value="">請選擇小工序</option>
              <option v-for="(process, index) in filteredMinorProcesses" :key="index" :value="process">
                {{ process.code }} {{ process.name }}
              </option>
            </select>
          </div>

          <!-- 工具 -->
          <div class="form-group">
            <label>工具：</label>
            <select v-model="processForm.tool">
              <option value="">請選擇工具</option>
              <option v-for="(tool, index) in toolOptions" :key="index" :value="tool">
                {{ tool }}
              </option>
            </select>
          </div>

          <!-- 耗材 -->
          <div class="form-group">
            <label>耗材：</label>
            <select v-model="processForm.consumable">
              <option value="">請選擇耗材</option>
              <option v-for="(consumable, index) in consumableOptions" :key="index" :value="consumable">
                {{ consumable }}
              </option>
            </select>
          </div>

          <!-- 數量 -->
          <div class="form-group">
            <label>數量：</label>
            <input type="number" v-model="processForm.quantity" min="1" step="1" />
          </div>

          <!-- 按鈕 -->
          <div class="form-actions">
            <!-- 左側按鈕：階段完成和取消階段完成 -->
            <div class="left-buttons">
              <button class="stage-complete-btn" @click="markStageCompleteWrapper" v-if="!isCurrentProcessCompleted && selectedProcessId && isCurrentProcessLastForMaterial">
                <i class="fas fa-check"></i> 階段完成
              </button>
              <button class="stage-incomplete-btn" @click="markStageIncompleteWrapper" v-if="isCurrentProcessCompleted && selectedProcessId">
                <i class="fas fa-undo"></i> 取消階段完成
              </button>
              <button class="merge-btn" @click="openMergeProcessModal" v-if="!isCurrentProcessCompleted && selectedProcessId && isCurrentProcessLastForMaterial && completedProcesses.length > 0">
                <i class="fas fa-link"></i> 合併工序
              </button>
              <button class="unmerge-btn" @click="cancelMerge" v-if="isCurrentProcessMerged">
                <i class="fas fa-unlink"></i> 取消合併
              </button>
            </div>

            <!-- 右側按鈕：其他功能按鈕 -->
            <div class="right-buttons">
              <button class="delete-btn" @click="deleteCurrentProcess" v-if="selectedProcessId">
                <i class="fas fa-trash"></i> 刪除工序
              </button>
              <button class="save-btn" @click="saveProcessWrapper">
                <i class="fas fa-save"></i> 保存
              </button>
              <button class="cancel-btn" @click="closeProcessModal">
                <i class="fas fa-times"></i> 取消
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加工序選單模態對話框 -->
    <div class="process-menu" v-if="processMenuVisible" :style="processMenuStyle">
      <div
        class="process-menu-item"
        :class="{ disabled: isProcessUsed(currentProcess) }"
        :disabled="isProcessUsed(currentProcess)"
        @click="!isProcessUsed(currentProcess) && addNextProcessWrapper()"
      >
        <i class="fas fa-plus"></i> 新增工序
      </div>
      <div class="process-menu-item" @click="editCurrentProcess">
        <i class="fas fa-edit"></i> 編輯
      </div>
      <div class="process-menu-item" @click="toggleFixProcessWrapper">
        <i class="fas fa-thumbtack"></i> {{ currentProcess && currentProcess.isFixed ? '取消固定' : '固定' }}
      </div>
    </div>

    <!-- 材料選單模態對話框 -->
    <div class="material-menu" v-if="materialMenuVisible" :style="materialMenuStyle">
      <div
        class="material-menu-item"
        :class="{ disabled: isMaterialUsed(currentMaterial) }"
        :disabled="isMaterialUsed(currentMaterial)"
        @click="!isMaterialUsed(currentMaterial) && addProcessForMaterial()"
      >
        <i class="fas fa-plus"></i> 新增工序
      </div>
      <div class="material-menu-item" @click="showProcessesForMaterial">
        <i class="fas fa-eye"></i> 顯示工序
      </div>
    </div>

    <!-- 合併工序選擇對話框 -->
    <div class="process-modal" v-if="mergeProcessModalVisible">
      <div class="process-modal-content">
        <div class="process-modal-header">
          <h3>選擇要合併的工序</h3>
          <button class="close-btn" @click="closeMergeProcessModal">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="process-modal-body">
          <div class="merge-process-list">
            <p v-if="completedProcesses.length === 0" class="no-processes">沒有可合併的工序</p>
            <div v-else>
              <p class="merge-instruction">請選擇一個已完成的工序進行合併：</p>
              <div class="process-selection-list">
                <div
                  v-for="process in completedProcesses"
                  :key="process.id"
                  class="process-selection-item"
                  :class="{ selected: selectedMergeProcessId === process.id }"
                  @click="selectedMergeProcessId = process.id"
                >
                  <div class="process-info">
                    <div class="process-name">{{ process.pieceName }}</div>
                    <div class="process-details">
                      {{ process.majorProcess }} - {{ process.minorProcess }}
                    </div>
                  </div>
                  <div class="process-status">
                    <i class="fas fa-check-circle" style="color: #10b981;"></i>
                  </div>
                </div>
              </div>

              <!-- 合併後的分片名稱輸入 -->
              <div class="form-group merge-name-input" v-if="selectedMergeProcessId">
                <label>合併後的分片名稱：</label>
                <input
                  type="text"
                  v-model="mergeProcessForm.pieceName"
                  placeholder="請輸入合併後的分片名稱"
                  class="merge-piece-name-input"
                />
                <div class="merge-name-preview" v-if="selectedMergeProcessId && mergeProcessForm.pieceName">
                  <div class="preview-title">預覽：</div>
                  <div class="preview-content">
                    <div class="original-names">
                      <div class="original-name">
                        {{ (savedProcesses.value || []).find(p => p.id === selectedProcessId.value)?.pieceName || '' }}
                      </div>
                      <div class="plus-sign">+</div>
                      <div class="original-name">
                        {{ (savedProcesses.value || []).find(p => p.id === selectedMergeProcessId)?.pieceName || '' }}
                      </div>
                    </div>
                    <div class="arrow-down">
                      <i class="fas fa-arrow-down"></i>
                    </div>
                    <div class="merged-name">
                      {{ mergeProcessForm.pieceName }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="form-actions" v-if="completedProcesses.length > 0">
            <div class="right-buttons">
              <button class="save-btn" @click="executeMergeProcess" :disabled="!selectedMergeProcessId || !mergeProcessForm.pieceName.trim()">
                <i class="fas fa-link"></i> 確認合併
              </button>
              <button class="cancel-btn" @click="closeMergeProcessModal">
                <i class="fas fa-times"></i> 取消
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量新增工序對話框 -->
    <div class="process-modal" v-if="batchProcessModalVisible">
      <div class="process-modal-content">
        <div class="process-modal-header">
          <h3>批量新增工序</h3>
          <button class="close-btn" @click="closeBatchProcessModal">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="process-modal-body">
          <!-- 選取的材料列表 -->
          <!-- 這一區塊整個移除，不再顯示材料清單 -->

          <!-- 分片名稱可編輯區塊 -->
          <div class="form-group">
            <label>分片名稱：</label>
            <div class="piece-name-preview">
              <div v-for="(material, index) in multiSelectedMaterials" :key="index" class="preview-item">
                <span class="material-label">{{ material }}</span>
                <input v-model="batchPieceNames[index]" style="width: 70%; margin-left: 8px;" />
              </div>
            </div>
          </div>

          <!-- 部位組織 -->
          <div class="form-group">
            <label>部位組織：</label>
            <select v-model="batchProcessForm.partStructure" @change="handleBatchPartStructureChange">
              <option value="">請選擇部位組織</option>
              <option v-for="(part, index) in partStructureList" :key="index" :value="part">
                {{ part.code }} {{ part.name }}
              </option>
            </select>
          </div>

          <!-- 分片組織 -->
          <div class="form-group">
            <label>分片組織：</label>
            <select v-model="batchProcessForm.pieceStructure">
              <option value="">請選擇分片組織</option>
              <option v-for="(piece, index) in batchFilteredPieceStructures" :key="index" :value="piece">
                {{ piece.code }} {{ piece.name }}
              </option>
            </select>
          </div>

          <!-- 材料 -->
          <div class="form-group">
            <label>材料：</label>
            <select v-model="batchProcessForm.material">
              <option value="">請選擇材料</option>
              <option v-for="(material, index) in materialList" :key="index" :value="material">
                {{ material.name }}
              </option>
            </select>
          </div>

          <!-- 大工序 -->
          <div class="form-group">
            <label>大工序：</label>
            <select v-model="batchProcessForm.majorProcess" @change="handleBatchMajorProcessChange">
              <option value="">請選擇大工序</option>
              <option v-for="(process, index) in filteredMajorProcesses" :key="index" :value="process">
                {{ process }}
              </option>
            </select>
          </div>

          <!-- 小工序 -->
          <div class="form-group">
            <label>小工序：</label>
            <select v-model="batchProcessForm.minorProcess">
              <option value="">請選擇小工序</option>
              <option v-for="(process, index) in batchFilteredMinorProcesses" :key="index" :value="process">
                {{ process.code }} {{ process.name }}
              </option>
            </select>
          </div>

          <!-- 工具 -->
          <div class="form-group">
            <label>工具：</label>
            <select v-model="batchProcessForm.tool">
              <option value="">請選擇工具</option>
              <option v-for="(tool, index) in toolOptions" :key="index" :value="tool">
                {{ tool }}
              </option>
            </select>
          </div>

          <!-- 耗材 -->
          <div class="form-group">
            <label>耗材：</label>
            <select v-model="batchProcessForm.consumable">
              <option value="">請選擇耗材</option>
              <option v-for="(consumable, index) in consumableOptions" :key="index" :value="consumable">
                {{ consumable }}
              </option>
            </select>
          </div>

          <!-- 數量 -->
          <div class="form-group">
            <label>數量：</label>
            <input type="number" v-model="batchProcessForm.quantity" min="1" step="1" />
          </div>

          <!-- 按鈕 -->
          <div class="form-actions">
            <div class="right-buttons">
              <button class="save-btn" @click="saveBatchProcess">
                <i class="fas fa-save"></i> 批量保存
              </button>
              <button class="cancel-btn" @click="closeBatchProcessModal">
                <i class="fas fa-times"></i> 取消
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import api from '../../services/api';
import { showError, showWarning, showSuccess, showSuccessToast, useNotification } from '../../services/notificationService';
import MaterialUsageView from './MaterialUsageView.vue';
import NextStepControl from '../../components/NextStepControl.vue';
import { createHistoryManager } from '../../utils/historyManager.js';
import {
  // 工序數據處理相關功能
  saveProcessesToLocalStorage,
  loadProcessesFromLocalStorage,
  updateProcessCompletionStatus,
  clearAllProcesses,
  deleteProcess,
  saveProcess,

  // 後端同步功能
  syncProcessesToBackend,
  loadProcessesFromBackend,

  // 表單處理相關功能
  handleMajorProcessChange,
  handleMinorProcessChange,
  handlePartStructureChange,

  // 工序選單相關功能
  showProcessMenu,
  addNextProcess,
  toggleFixProcess
} from '../../utils/processPreparationUtils';
// 引入靜態數據模組
import {
  majorProcesses,
  defaultPositionNames,
  partStructureList,
  pieceStructureList,
  materialList,
  minorProcessList,
  toolList,
  consumableList
} from '../../utils/processPreparationData';

const router = useRouter();
const route = useRoute();
const bomId = ref(route.params.bomId);
const notification = useNotification();

// 用戶相關信息
const currentUserDepartment = ref(-1);
const isAdmin = ref(false);
const bomConfirmationStatus = ref(0);

// 權限控制
const canEditProcess = computed(() => {
  // 檢查BOM確認狀態，只有未確認狀態(0)才能編輯
  if (Number(bomConfirmationStatus.value) !== 0) {
    return false;
  }

  const isDept0 = currentUserDepartment.value === 0;
  const canEdit = !isDept0;
  return canEdit; // 非台北部部門可編輯
});

// 部位名稱列表
const positionNames = ref([]);

// 初始化部位名稱
positionNames.value = defaultPositionNames;

// 顏色列表（20種）
const materialColors = [
  '#f87171', '#fbbf24', '#34d399', '#60a5fa', '#a78bfa',
  '#f472b6', '#facc15', '#4ade80', '#38bdf8', '#818cf8',
  '#fb7185', '#fcd34d', '#6ee7b7', '#93c5fd', '#c4b5fd',
  '#f9a8d4', '#fde68a', '#5eead4', '#7dd3fc', '#a5b4fc'
];
// 根據材料名稱分配顏色（hash），並確保相鄰不重複
function getMaterialColorsForList(list) {
  const result = [];
  let lastColorIdx = -1;
  for (let i = 0; i < list.length; i++) {
    const name = list[i];
    // 先用 hash 決定顏色
    let hash = 0;
    for (let j = 0; j < name.length; j++) {
      hash = name.charCodeAt(j) + ((hash << 5) - hash);
    }
    let colorIdx = Math.abs(hash) % materialColors.length;
    // 如果跟上一個顏色一樣，往後找一格
    if (colorIdx === lastColorIdx) {
      colorIdx = (colorIdx + 1) % materialColors.length;
    }
    result.push(materialColors[colorIdx]);
    lastColorIdx = colorIdx;
  }
  return result;
}
// 根據材料名稱分配顏色（hash）
function getMaterialColor(name) {
  if (!name) return materialColors[0];
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  return materialColors[Math.abs(hash) % materialColors.length];
}

// 取得指定材料名稱的顏色（與材料區一致）
function getMaterialColorByName(name) {
  if (!name) return materialColors[0];
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  return materialColors[Math.abs(hash) % materialColors.length];
}


// 材料用量表相關
const materialUsageVisible = ref(false);
// 存儲材料區域已選擇的部位組織和分片組織
const selectedMaterialStructure = ref({
  partStructure: null,
  pieceStructure: null
});

// 打開材料用量表
const openMaterialUsage = () => {
  materialUsageVisible.value = true;
};

// 關閉材料用量表
const closeMaterialUsage = () => {
  materialUsageVisible.value = false;
  // 關閉後獲取部位名稱
  fetchPositionNames();
};

// 處理材料選擇
const handleMaterialSelected = (materials) => {
  // 如果材料中包含部位組織和分片組織信息，則保存起來
  if (materials && materials.length > 0) {
    const material = materials[0];
    if (material.partStructure || material.pieceStructure) {
      selectedMaterialStructure.value = {
        partStructure: material.partStructure || selectedMaterialStructure.value.partStructure,
        pieceStructure: material.pieceStructure || selectedMaterialStructure.value.pieceStructure
      };

      // 顯示視覺反饋，提示用戶已自動保存組織信息
      notification.success(`已自動保存材料「${material.usagePosition || ''}」的組織信息，將用於新工序`);
    }
  }
};

// 返回BOM頁面
const backToBom = () => {
  router.push({ name: 'design-bom' });
};

// 工序模態對話框相關
const processModalVisible = ref(false);
const selectedPosition = ref('');

// 批量新增工序對話框相關
const batchProcessModalVisible = ref(false);
const batchProcessForm = ref({
  pieceName: '',
  partStructure: '',
  pieceStructure: '',
  material: '',
  majorProcess: '',
  minorProcess: '',
  tool: '',
  consumable: '',
  quantity: 1
});

// 工序表單數據
const processForm = ref({
  pieceName: '',
  partStructure: '',
  pieceStructure: '',
  material: '',
  majorProcess: '',
  minorProcess: '',
  tool: '',
  consumable: '',
  quantity: 1
});

// 歷史記錄管理器
const historyManager = createHistoryManager(20, bomId.value);

// 已選擇的已完成工序


// 用來追蹤每個大工序的計數器
const processCounters = ref({});

// 材料區域折疊狀態
const materialCollapsed = ref(true);

// 新增：追蹤滑鼠懸停的材料
const hoveredMaterial = ref(null);
const hoveredProcess = ref(null);

function isMaterialHighlighted(materialName) {
  // 滑鼠在材料區
  if (hoveredMaterial.value) {
    return materialName === hoveredMaterial.value;
  }
  // 滑鼠在工序
  if (hoveredProcess.value) {
    const current = savedProcesses.value.find(p => p.id === hoveredProcess.value);
    if (!current) return false;
    return materialName === current.pieceName;
  }
  // 工序被固定時，該工序對應的材料也高亮
  const fixed = savedProcesses.value.find(p => p.isFixed);
  if (fixed) {
    return materialName === fixed.pieceName;
  }
  return false;
}

function isProcessHighlighted(proc) {
  // 滑鼠在材料區
  if (hoveredMaterial.value) {
    // 需求：材料高亮時，工序不需要特別高亮
    return false;
  }
  // 滑鼠在工序
  if (hoveredProcess.value) {
    const current = savedProcesses.value.find(p => p.id === hoveredProcess.value);
    if (!current) return false;
    const all = savedProcesses.value.filter(p => p.pieceName === current.pieceName).sort((a, b) => a.id - b.id);
    const idx = all.findIndex(p => p.id === current.id);
    if (idx === -1) return false;
    return proc.id === current.id || proc.id === (all[idx - 1]?.id) || proc.id === (all[idx + 1]?.id);
  }
  // 工序被固定時，該工序及其同材料的前後工序也高亮
  const fixed = savedProcesses.value.find(p => p.isFixed);
  if (fixed) {
    const all = savedProcesses.value.filter(p => p.pieceName === fixed.pieceName).sort((a, b) => a.id - b.id);
    const idx = all.findIndex(p => p.id === fixed.id);
    if (idx === -1) return false;
    return proc.id === fixed.id || proc.id === (all[idx - 1]?.id) || proc.id === (all[idx + 1]?.id);
  }
  return false;
}
function shouldDimProcessByProcess(proc) {
  if (hoveredMaterial.value) {
    // 需求：材料區 hover 時不再讓工序半透明
    return false;
  }
  if (hoveredProcess.value) {
    const current = savedProcesses.value.find(p => p.id === hoveredProcess.value);
    if (!current) return false;
    const all = savedProcesses.value.filter(p => p.pieceName === current.pieceName).sort((a, b) => a.id - b.id);
    const idx = all.findIndex(p => p.id === current.id);
    if (idx === -1) return false;
    return proc.pieceName === current.pieceName && proc.id !== current.id && proc.id !== (all[idx - 1]?.id) && proc.id !== (all[idx + 1]?.id);
  }
  // 工序被固定時，該材料的非前後工序都 dim
  const fixed = savedProcesses.value.find(p => p.isFixed);
  if (fixed) {
    const all = savedProcesses.value.filter(p => p.pieceName === fixed.pieceName).sort((a, b) => a.id - b.id);
    const idx = all.findIndex(p => p.id === fixed.id);
    if (idx === -1) return false;
    return proc.pieceName === fixed.pieceName && proc.id !== fixed.id && proc.id !== (all[idx - 1]?.id) && proc.id !== (all[idx + 1]?.id);
  }
  return false;
}


// 自動填充材料區域已選擇的組織信息
const fillMaterialStructureInfo = () => {
  if (!processForm.value.partStructure && selectedMaterialStructure.value.partStructure) {
    // 查找對應的部位組織對象
    const partStructure = partStructureListRef.value.find(
      part => part.code === selectedMaterialStructure.value.partStructure
    );
    if (partStructure) {
      processForm.value.partStructure = partStructure;

      // 如果有分片組織，也自動填充
      if (selectedMaterialStructure.value.pieceStructure) {
        // 找到對應的分片組織
        let pieceStructure = null;
        // 遍歷所有部位的分片組織
        Object.values(pieceStructureListRef.value).forEach(pieces => {
          const found = pieces.find(p => p.code === selectedMaterialStructure.value.pieceStructure);
          if (found) {
            pieceStructure = found;
          }
        });

        if (pieceStructure) {
          processForm.value.pieceStructure = pieceStructure;
        }
      }
    }
  }
};

// 選擇部位來開始工序
const toggleSelectPosition = async (position) => {
  // 保存當前狀態到歷史記錄
      await saveCurrentStateToHistory(`開始編輯工序：${position}`);

  // 打開工序選擇模態對話框
  selectedPosition.value = position;
  processForm.value.pieceName = position; // 預設分片名稱為部位名稱
  processModalVisible.value = true;

  // 重置限制
  currentProcessRestriction.value = null;

  // 重置來源工序 ID
  selectedProcessId.value = null;

  // 自動填充材料區域已選擇的組織信息
  fillMaterialStructureInfo();
};

// 從現有工序選擇下一個工序
const currentProcessRestriction = ref(null);
const selectedProcessId = ref(null);

// 計算屬性：判斷當前選擇的工序是否已完成
const isCurrentProcessCompleted = computed(() => {
  if (!selectedProcessId.value) return false;

  const selectedProcess = savedProcesses.value.find(p => p.id === selectedProcessId.value);
  return selectedProcess ? selectedProcess.isCompleted : false;
});

// 計算屬性：判斷當前選擇的工序是否是合併的
const isCurrentProcessMerged = computed(() => {
  if (!selectedProcessId.value) return false;

  const selectedProcess = savedProcesses.value.find(p => p.id === selectedProcessId.value);
  return selectedProcess ? (selectedProcess.originalSources && selectedProcess.originalSources.length > 0) : false;
});

// 計算屬性：判斷當前選擇的工序是否為該材料的最後一個工序
const isCurrentProcessLastForMaterial = computed(() => {
  if (!selectedProcessId.value) return false;

  const selectedProcess = savedProcesses.value.find(p => p.id === selectedProcessId.value);
  if (!selectedProcess) return false;

  // 找到該材料的所有工序
  const materialProcesses = savedProcesses.value.filter(p => p.pieceName === selectedProcess.pieceName);

  // 如果只有一個工序，那就是最後一個
  if (materialProcesses.length === 1) return true;

  // 檢查是否有其他工序以此工序為來源
  const hasNextProcess = savedProcesses.value.some(p => p.sourceProcessId === selectedProcess.id);

  return !hasNextProcess;
});

// 計算屬性：獲取已完成的工序列表（用於合併工序選擇）
const completedProcesses = computed(() => {
  if (!savedProcesses.value || !Array.isArray(savedProcesses.value)) return [];

  return savedProcesses.value.filter(process =>
    process.isCompleted &&
    process.id !== selectedProcessId.value && // 排除當前工序
    isLastProcessForMaterial(process) // 只顯示每個材料的最後一個工序
  );
});

// 判斷工序是否為該材料的最後一個工序
const isLastProcessForMaterial = (process) => {
  if (!process || !savedProcesses.value || !Array.isArray(savedProcesses.value)) return false;

  // 檢查是否有其他工序以此工序為來源
  const hasNextProcess = savedProcesses.value.some(p => p.sourceProcessId === process.id);

  return !hasNextProcess;
};





// 關閉工序模態對話框
const closeProcessModal = () => {
  processModalVisible.value = false;
  // 重置表單數據
  processForm.value = {
    pieceName: '',
    partStructure: '',
    pieceStructure: '',
    material: '',
    order: '',
    majorProcess: '',
    minorProcess: '',
    tool: '',
    consumable: '',
    quantity: 1
  };
  // 重置計數器
  processCounters.value = {};

  // 重置限制和來源工序 ID
  currentProcessRestriction.value = null;
  selectedProcessId.value = null;

  // 重置已選擇的已完成工序
  
};

// 已保存的工序列表
const savedProcesses = ref([]);



// 從 localStorage 或後端加載工序列表
const loadProcessesWrapper = async () => {
  try {
    // 先嘗試從後端加載
    const backendProcesses = await loadProcessesFromBackend(bomId.value, api, minorProcessListRef.value);
    
    if (backendProcesses && backendProcesses.length > 0) {
      savedProcesses.value = backendProcesses;
      
      // 保存到 localStorage 作為備份
      saveProcessesToLocalStorage(bomId.value, savedProcesses.value);
      
      // 確保關聯數據正確設置
      ensureProcessRelations();
      return;
    }
    
    // 如果後端沒有數據，則從 localStorage 加載
    const localProcesses = loadProcessesFromLocalStorage(bomId.value);
    if (localProcesses && localProcesses.length > 0) {
      savedProcesses.value = localProcesses;
      console.log('從 localStorage 加載了工序列表:', localProcesses.length);
      
      // 將本地數據同步到後端
      syncProcessesToBackend(savedProcesses.value, bomId.value, api).then(result => {
        if (result) {
          console.log('已將本地工序同步到後端');
        }
      }).catch(error => {
        console.error('同步本地工序到後端失敗:', error);
      });
      
      // 確保關聯數據正確設置
      ensureProcessRelations();
    }
  } catch (error) {
    console.error('加載工序列表失敗:', error);
    // 如果出錯，嘗試從 localStorage 加載
    const localProcesses = loadProcessesFromLocalStorage(bomId.value);
    if (localProcesses && localProcesses.length > 0) {
      savedProcesses.value = localProcesses;
      // 確保關聯數據正確設置
      ensureProcessRelations();
    }
  }
};

// 確保工序關聯數據正確設置
const ensureProcessRelations = () => {
  // 檢查每個工序的sourceProcessId是否有指向存在的工序
  // 以及設置正確的sourcePosition
  savedProcesses.value.forEach(process => {
    // 如果沒有sourceProcessId但有sourcePosition，確保sourcePosition是正確的
    if (!process.sourceProcessId && !process.sourcePosition) {
      // 預設使用pieceName作為sourcePosition
      process.sourcePosition = process.pieceName;
    }
    
    // 如果有sourceProcessId，確認是否指向存在的工序
    if (process.sourceProcessId) {
      const sourceProcessExists = savedProcesses.value.some(p => p.id === process.sourceProcessId);
      if (!sourceProcessExists) {
        console.warn(`工序 ${process.pieceName} 的來源工序ID不存在:${process.sourceProcessId}，重設為使用部位名稱`);
        process.sourceProcessId = null;
        process.sourcePosition = process.pieceName;
      }
    }
  });
  
  // 保存調整後的關係
  saveProcessesToLocalStorage(bomId.value, savedProcesses.value);
};

// 使用從 utils 導入的 updateProcessCompletionStatus 函數
// 包裝函數，以便傳遞正確的參數
const updateProcessCompletionStatusWrapper = (processId, isCompleted) => {
  if (!processId) return false;

  // 使用從 utils 導入的 updateProcessCompletionStatus 函數
  const result = updateProcessCompletionStatus(savedProcesses.value, processId, isCompleted);

  if (result) {
    // 保存到 localStorage
    saveProcessesToLocalStorage(bomId.value, savedProcesses.value);

    // 顯示成功訊息
    const process = savedProcesses.value.find(p => p.id === processId);
    const statusMsg = isCompleted ? '已標記為完成' : '已取消階段完成標記';
    showSuccess(`工序 ${process.pieceName} ${statusMsg}`);
  }

  return result;
};

// 使用從 utils 導入的 markStageComplete 函數
// 包裝函數，以便傳遞正確的參數
const markStageCompleteWrapper = async () => {
  if (updateProcessCompletionStatusWrapper(selectedProcessId.value, true)) {
    // 同步到後端
    syncProcessesToBackend(savedProcesses.value, bomId.value, api).catch(error => {
      console.error('同步工序到後端失敗:', error);
    });

    // 保存當前狀態到歷史記錄
    const process = savedProcesses.value.find(p => p.id === selectedProcessId.value);
    await saveCurrentStateToHistory(`標記工序完成：${process ? process.pieceName : '未知工序'}`);

    // 關閉模態對話框
    closeProcessModal();
  }
};

// 取消標記工序階段完成
const markStageIncompleteWrapper = async () => {
  if (updateProcessCompletionStatusWrapper(selectedProcessId.value, false)) {
    // 同步到後端
    syncProcessesToBackend(savedProcesses.value, bomId.value, api).catch(error => {
      console.error('同步工序到後端失敗:', error);
    });

    // 保存當前狀態到歷史記錄
    const process = savedProcesses.value.find(p => p.id === selectedProcessId.value);
    await saveCurrentStateToHistory(`取消工序完成：${process ? process.pieceName : '未知工序'}`);

    // 關閉模態對話框
    closeProcessModal();
  }
};

// 合併工序功能
const mergeProcessModalVisible = ref(false);
const selectedMergeProcessId = ref(null);
const mergeProcessForm = ref({
  pieceName: ''
});

// 打開合併工序選擇對話框
const openMergeProcessModal = () => {
  if (!selectedProcessId.value) return;

  // 檢查是否為最後一個工序
  if (!isCurrentProcessLastForMaterial.value) {
    showError('只有該材料的最後一個工序才能進行合併');
    return;
  }

  // 重置表單
  mergeProcessForm.value.pieceName = '';
  selectedMergeProcessId.value = null;

  mergeProcessModalVisible.value = true;
};

// 關閉合併工序選擇對話框
const closeMergeProcessModal = () => {
  mergeProcessModalVisible.value = false;
  selectedMergeProcessId.value = null;
  mergeProcessForm.value.pieceName = '';
};

// 執行合併工序
const executeMergeProcess = async () => {
  if (!selectedProcessId.value || !selectedMergeProcessId.value) return;

  // 檢查分片名稱是否已填入
  if (!mergeProcessForm.value.pieceName.trim()) {
    showError('請輸入合併後的分片名稱');
    return;
  }

  const currentProcessIndex = savedProcesses.value.findIndex(p => p.id === selectedProcessId.value);
  const mergeProcessIndex = savedProcesses.value.findIndex(p => p.id === selectedMergeProcessId.value);

  if (currentProcessIndex === -1 || mergeProcessIndex === -1) {
    showError('找不到要合併的工序');
    return;
  }

  const currentProcess = savedProcesses.value[currentProcessIndex];
  const mergeProcess = savedProcesses.value[mergeProcessIndex];

  // 儲存原始材料名稱
  const originalCurrentMaterialName = currentProcess.pieceName;
  const originalMergeMaterialName = mergeProcess.pieceName;

  // 儲存原始來源信息
  const originalSourceProcessId = currentProcess.sourceProcessId;
  const originalSourcePosition = currentProcess.sourcePosition;
  const originalIsCompleted = currentProcess.isCompleted;

  // 儲存被合併的工序的完成狀態
  const mergedProcessIsCompleted = mergeProcess.isCompleted;

  // 創建或更新原始來源陣列
  if (!currentProcess.originalSources) {
    currentProcess.originalSources = [];
  }

  // 將原本的來源信息和完成狀態添加到原始來源陣列中
  if (originalSourceProcessId) {
    currentProcess.originalSources.push({
      sourceProcessId: originalSourceProcessId,
      sourcePosition: null,
      isCompleted: originalIsCompleted,
      mergedProcessId: mergeProcess.id,
      mergedProcessIsCompleted: mergedProcessIsCompleted,
      originalCurrentMaterialName: originalCurrentMaterialName,
      originalMergeMaterialName: originalMergeMaterialName
    });
  } else if (originalSourcePosition) {
    currentProcess.originalSources.push({
      sourceProcessId: null,
      sourcePosition: originalSourcePosition,
      isCompleted: originalIsCompleted,
      mergedProcessId: mergeProcess.id,
      mergedProcessIsCompleted: mergedProcessIsCompleted,
      originalCurrentMaterialName: originalCurrentMaterialName,
      originalMergeMaterialName: originalMergeMaterialName
    });
  }

  // 將當前工序的來源設置為被合併的工序
  currentProcess.sourceProcessId = mergeProcess.id;
  currentProcess.sourcePosition = null;

  // 更新當前工序的分片名稱為新的合併名稱
  currentProcess.pieceName = mergeProcessForm.value.pieceName.trim();

  // 取消兩個工序的階段完成狀態
  currentProcess.isCompleted = false;
  mergeProcess.isCompleted = false;

  // 保存到 localStorage
  saveProcessesToLocalStorage(bomId.value, savedProcesses.value);

  // 同步到後端
  syncProcessesToBackend(savedProcesses.value, bomId.value, api).catch(error => {
    console.error('同步工序到後端失敗:', error);
  });

  // 保存當前狀態到歷史記錄
  await saveCurrentStateToHistory(`合併工序：${originalMergeMaterialName} 合併到 ${originalCurrentMaterialName}，新名稱為 ${mergeProcessForm.value.pieceName}`);

  // 顯示成功訊息
  showSuccess(`已將工序 ${originalMergeMaterialName} 合併到 ${originalCurrentMaterialName}，合併後命名為 ${mergeProcessForm.value.pieceName}`);

  // 關閉模態對話框
  closeMergeProcessModal();
  closeProcessModal();
};

// 取消合併
const cancelMerge = async () => {
  if (!selectedProcessId.value) return;

  if (confirm('確定要取消合併嗎？此操作無法撤銷。')) {
    // 找到當前工序
    const processIndex = savedProcesses.value.findIndex(p => p.id === selectedProcessId.value);

    if (processIndex !== -1) {
      const process = savedProcesses.value[processIndex];

      // 檢查是否有原始來源
      if (process.originalSources && process.originalSources.length > 0) {
        // 取最後一個原始來源（即合併前的來源）
        const lastSource = process.originalSources[process.originalSources.length - 1];

        // 儲存取消合併前的狀態資訊，用於歷史記錄描述
        const currentMergedName = process.pieceName;
        const originalCurrentName = lastSource.originalCurrentMaterialName;
        const originalMergeName = lastSource.originalMergeMaterialName;

        // 將來源設回合併前的狀態
        if (lastSource.sourceProcessId) {
          process.sourceProcessId = lastSource.sourceProcessId;
          process.sourcePosition = null;
        } else if (lastSource.sourcePosition) {
          process.sourcePosition = lastSource.sourcePosition;
          process.sourceProcessId = null;
        }

        // 恢復原始材料名稱
        if (lastSource.originalCurrentMaterialName) {
          process.pieceName = lastSource.originalCurrentMaterialName;
        }

        // 恢復工序的完成狀態（如果有存儲）
        if (lastSource.isCompleted !== undefined) {
          process.isCompleted = lastSource.isCompleted;
        }

        // 如果有合併的工序 ID，則恢復其完成狀態
        if (lastSource.mergedProcessId && lastSource.mergedProcessIsCompleted !== undefined) {
          // 找到合併的工序
          const mergedProcessIndex = savedProcesses.value.findIndex(p => p.id === lastSource.mergedProcessId);
          if (mergedProcessIndex !== -1) {
            savedProcesses.value[mergedProcessIndex].isCompleted = lastSource.mergedProcessIsCompleted;
          }
        }

        // 移除最後一個原始來源
        process.originalSources.pop();

        // 如果原始來源陣列為空，則移除它
        if (process.originalSources.length === 0) {
          delete process.originalSources;
        }

        // 保存到 localStorage
        saveProcessesToLocalStorage(bomId.value, savedProcesses.value);

        // 同步到後端
        syncProcessesToBackend(savedProcesses.value, bomId.value, api).catch(error => {
          console.error('同步工序到後端失敗:', error);
        });

        // 保存當前狀態到歷史記錄
        await saveCurrentStateToHistory(`取消合併：將「${currentMergedName}」恢復為「${originalCurrentName}」和「${originalMergeName}」`);

        // 顯示成功訊息
        showSuccess('已成功取消合併');

        // 關閉模態對話框
        closeProcessModal();
      }
    }
  }
};

// 使用從 utils 導入的 clearAllProcesses 函數
// 包裝函數，以便傳遞正確的參數
const clearAllProcessesWrapper = async () => {
  if (confirm('確定要清除所有工序嗎？此操作無法撤銷。')) {
    // 使用從 utils 導入的 clearAllProcesses 函數
    clearAllProcesses(
      savedProcesses.value,
      bomId.value,
      processCounters.value,
      null, // 連接線功能已移除
      null, // 連接線功能已移除
      api // 傳入 api 對象
    );

    // 保存當前狀態到歷史記錄
    await saveCurrentStateToHistory('清除所有工序');

    showSuccess('已清除所有工序');
  }
};

// 使用從 utils 導入的 deleteProcess 函數
// 包裝函數，以便傳遞正確的參數
const deleteCurrentProcess = async () => {
  if (!selectedProcessId.value) return;

  if (confirm('確定要刪除這個工序嗎？此操作無法撤銷。')) {
    // 使用從 utils 導入的 deleteProcess 函數
    const result = deleteProcess(
      savedProcesses.value,
      selectedProcessId.value,
      bomId.value,
      null, // 連接線功能已移除
      null, // 連接線功能已移除
      api // 傳入 api 對象
    );

    if (result) {
      // 保存當前狀態到歷史記錄
      await saveCurrentStateToHistory('刪除工序');

      // 顯示成功訊息
      showSuccess('工序已成功刪除');

      // 關閉模態對話框
      closeProcessModal();
    }
  }
};



// 使用從 utils 導入的 saveProcess 函數
// 包裝函數，以便傳遞正確的參數
const saveProcessWrapper = async () => {
  // 驗證必填欄位
  if (!processForm.value.pieceName) {
    showError('請輸入分片名稱');
    return;
  }



  // 使用從 utils 導入的 saveProcess 函數
  const result = saveProcess(
    savedProcesses.value,
    processForm.value,
    selectedProcessId.value,
    selectedPosition.value,
    null, // 移除產生後序號
    processCounters.value,
    bomId.value,
    api // 傳入 api 對象
  );

  if (result) {
    // 保存當前狀態到歷史記錄
    await saveCurrentStateToHistory('保存工序');

    // 顯示成功訊息
    if (selectedProcessId.value) {
      showSuccess('工序已成功更新');
    } else {
      showSuccess('工序已成功保存');
    }

    // 關閉模態對話框
    closeProcessModal();
  }
};

// 歷史記錄相關方法
const getCurrentEditState = () => {
  return {
    processForm: JSON.parse(JSON.stringify(processForm.value)),
    savedProcesses: JSON.parse(JSON.stringify(savedProcesses.value)),
    selectedProcessId: selectedProcessId.value,
    selectedPosition: selectedPosition.value,
    processCounters: JSON.parse(JSON.stringify(processCounters.value)),
    materialCollapsed: materialCollapsed.value,
    filteredMaterial: [...filteredMaterial.value],
    isMultiSelectMode: isMultiSelectMode.value,
    multiSelectedMaterials: [...multiSelectedMaterials.value],
    multiSelectedProcesses: [...multiSelectedProcesses.value]
  };
};

const restoreEditState = (state) => {
  if (!state || !state.state) return;

  const editState = state.state;

  // 恢復表單數據
  processForm.value = { ...editState.processForm };
  savedProcesses.value = [...editState.savedProcesses];
  selectedProcessId.value = editState.selectedProcessId;
  selectedPosition.value = editState.selectedPosition;
  processCounters.value = { ...editState.processCounters };
  materialCollapsed.value = editState.materialCollapsed;
  filteredMaterial.value = [...editState.filteredMaterial];
  isMultiSelectMode.value = editState.isMultiSelectMode;
  multiSelectedMaterials.value = [...editState.multiSelectedMaterials];
  multiSelectedProcesses.value = [...editState.multiSelectedProcesses];

  // 保存到localStorage
  saveProcessesToLocalStorage(bomId.value, savedProcesses.value);

  // 同步到後端以確保數據一致性
  syncProcessesToBackend(savedProcesses.value, bomId.value, api).catch(error => {
    console.error('恢復狀態時同步到後端失敗:', error);
  });

  // 更新連接線
  nextTick(() => {
    updateConnectionLines();
  });
};

const saveCurrentStateToHistory = async (description = '') => {
  const currentState = getCurrentEditState();
  await historyManager.saveState(currentState, description);
};

const handleHistoryStateChanged = (historyState) => {
  restoreEditState(historyState);
  showSuccessToast(`已恢復到：${historyState.description}`);
};

const handleClearHistory = async () => {
  await historyManager.clear();
  showSuccessToast('歷史記錄已清除');
};

// 將引入的靜態數據轉換為 ref 對象
const partStructureListRef = ref(partStructureList);
const pieceStructureListRef = ref(pieceStructureList);
const minorProcessListRef = ref(minorProcessList);
const toolListRef = ref(toolList);
const consumableListRef = ref(consumableList);

// 動態計算材料顏色，確保相鄰不重複
const materialColorsForList = computed(() => getMaterialColorsForList(positionNames.value));

// 計算屬性：過濾後的大工序列表
const filteredMajorProcesses = computed(() => {
  // 不管什麼階段，都可以選擇所有的大工序
  return majorProcesses;
});

// 計算屬性：過濾後的小工序列表
const filteredMinorProcesses = computed(() => {
  if (processForm.value.majorProcess) {
    return minorProcessListRef.value[processForm.value.majorProcess] || [];
  }
  return [];
});

// 計算屬性：批量新增對話框的過濾後小工序列表
const batchFilteredMinorProcesses = computed(() => {
  if (batchProcessForm.value.majorProcess) {
    return minorProcessListRef.value[batchProcessForm.value.majorProcess] || [];
  }
  return [];
});

// 計算屬性：過濾後的分片組織列表
const filteredPieceStructures = computed(() => {
  // 將 pieceStructureList 物件的所有值（陣列）合併成一個單一陣列
  const allPieces = Object.values(pieceStructureListRef.value).flat();
  const uniquePieces = [];
  const seenCodes = new Set();
  for (const piece of allPieces) {
    if (!seenCodes.has(piece.code)) {
      uniquePieces.push(piece);
      seenCodes.add(piece.code);
    }
  }
  return uniquePieces;
});

// 計算屬性：批量新增對話框的過濾後分片組織列表
const batchFilteredPieceStructures = computed(() => {
  // 將 pieceStructureList 物件的所有值（陣列）合併成一個單一陣列
  const allPieces = Object.values(pieceStructureListRef.value).flat();
  const uniquePieces = [];
  const seenCodes = new Set();
  for (const piece of allPieces) {
    if (!seenCodes.has(piece.code)) {
      uniquePieces.push(piece);
      seenCodes.add(piece.code);
    }
  }
  return uniquePieces;
});

// 計算當前工序可用的工具選項
const toolOptions = computed(() => {
  if (processForm.value.majorProcess && processForm.value.minorProcess) {
    const processKey = `${processForm.value.majorProcess}-${processForm.value.minorProcess.code}`;
    return toolListRef.value[processKey] || [];
  }
  return [];
});

// 計算當前工序可用的耗材選項
const consumableOptions = computed(() => {
  if (processForm.value.majorProcess && processForm.value.minorProcess) {
    const processKey = `${processForm.value.majorProcess}-${processForm.value.minorProcess.code}`;
    return consumableListRef.value[processKey] || [];
  }
  return [];
});

// 使用從 utils 導入的 handleMajorProcessChange 函數
// 包裝函數，以便傳遞正確的參數
const handleMajorProcessChangeWrapper = () => {
  handleMajorProcessChange(processForm.value);
};

// 使用從 utils 導入的 handleMinorProcessChange 函數
// 包裝函數，以便傳遞正確的參數
const handleMinorProcessChangeWrapper = () => {
  handleMinorProcessChange(processForm.value);
};

// 使用從 utils 導入的 handlePartStructureChange 函數
// 包裝函數，以便傳遞正確的參數
const handlePartStructureChangeWrapper = () => {
  handlePartStructureChange(processForm.value);
};

// 獲取部位名稱
const fetchPositionNames = async () => {
  try {
    // 獲取所有部位名稱（主料、副料和其他）
    const allPositions = [];

    // 獲取主料部位名稱
    const mainResponse = await api.materialDetailSheet.getItems({
      bomId: bomId.value,
      sheetType: '主料'
    });

    if (mainResponse && mainResponse.status === 'success' && Array.isArray(mainResponse.data)) {
      const mainPositions = mainResponse.data
        .map(item => item.position_name)
        .filter(name => name && name.trim() !== '');
      allPositions.push(...mainPositions);
    }

    // 獲取副料部位名稱
    const subResponse = await api.materialDetailSheet.getItems({
      bomId: bomId.value,
      sheetType: '副料'
    });

    if (subResponse && subResponse.status === 'success' && Array.isArray(subResponse.data)) {
      const subPositions = subResponse.data
        .map(item => item.position_name)
        .filter(name => name && name.trim() !== '');
      allPositions.push(...subPositions);
    }

    // 獲取其他部位名稱
    const otherResponse = await api.materialDetailSheet.getItems({
      bomId: bomId.value,
      sheetType: '其他'
    });

    if (otherResponse && otherResponse.status === 'success' && Array.isArray(otherResponse.data)) {
      const otherPositions = otherResponse.data
        .map(item => item.position_name)
        .filter(name => name && name.trim() !== '');
      allPositions.push(...otherPositions);
    }

    // 去重
    const uniquePositions = [...new Set(allPositions)];

    // 更新部位名稱列表
    positionNames.value = uniquePositions;
  } catch (error) {
    console.error('獲取部位名稱失敗:', error);
    showError('獲取部位名稱失敗');
  }
};

// 獲取當前用戶信息
const fetchCurrentUser = async () => {
  try {
    const response = await api.getCurrentUser();
    if (response.status === 'success' && response.data && response.data.employee) {
      currentUserDepartment.value = parseInt(response.data.employee.department);
      isAdmin.value = response.data.employee.role === 0;
    } else {
      // 如果API獲取失敗，嘗試從localStorage獲取
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          currentUserDepartment.value = parseInt(user.department);
          isAdmin.value = user.role === 0;
        } catch (error) {
          console.error('解析用戶資訊出錯:', error);
        }
      }
    }
  } catch (error) {
    console.error('獲取使用者資訊時發生錯誤:', error);
    // 嘗試從localStorage獲取
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        currentUserDepartment.value = parseInt(user.department);
        isAdmin.value = user.role === 0;
      } catch (error) {
        console.error('解析用戶資訊出錯:', error);
      }
    }
  }
};

// 獲取BOM資訊，檢查確認狀態
const fetchBomInfo = async () => {
  try {
    if (!bomId.value) return;

    const response = await api.bom.getById(bomId.value);

    if (response.status === 'success' && response.data) {
      bomConfirmationStatus.value = Number(response.data.confirmationStatus || 0);

      // 如果BOM不是未確認狀態，顯示警告
      if (bomConfirmationStatus.value !== 0) {
        showWarning('注意：此BOM已經被確認，無法進行編輯操作。');
      }
    }
  } catch (error) {
    console.error('獲取BOM資訊失敗:', error);
    showWarning('無法獲取BOM確認狀態');
  }
};

// 根據大工序獲取工序列表
const getProcessesByMajorProcess = (majorProcess) => {
  if (!majorProcess || !savedProcesses.value) return [];
  if (filteredMaterial.value.length > 0) {
    return savedProcesses.value.filter(
      process => process && process.majorProcess === majorProcess && filteredMaterial.value.includes(process.pieceName)
    );
  }
  return savedProcesses.value.filter(process => process && process.majorProcess === majorProcess);
};

// 獲取工序按照材料來源的順序序號（01, 02, 03...）
const getProcessIndex = (process, majorProcess) => {
  if (!process || !majorProcess) return '';

  // 取得所有工序中，相同材料來源的工序（跨大工序）
  const allProcesses = savedProcesses.value;
  // 用 filter 保持原始順序，不做 sort
  const sameSourceProcesses = allProcesses.filter(p => p.sourcePosition === process.sourcePosition);

  // 在相同材料來源的工序中找到當前工序的索引
  const index = sameSourceProcesses.findIndex(p => p.id === process.id);
  if (index === -1) return '';

  // 返回兩位數格式的序號
  return (index + 1).toString().padStart(2, '0');
};

// 獲取小工序名稱的輔助函數
const getMinorProcessName = (process) => {
  if (!process) return '';
  
  // 如果 minorProcess 不存在，則返回空字串
  if (!process.minorProcess) return '';

  // 檢查 minorProcess 是不是物件
  if (typeof process.minorProcess === 'object') {
    // 如果是物件且有 name 屬性，直接返回 name
    if (process.minorProcess.name) {
      return process.minorProcess.name;
    }
    // 如果只有 code 屬性，也返回它
    if (process.minorProcess.code) {
      return process.minorProcess.code;
    }
  }

  // 如果 minorProcess 是字串，則嘗試在 minorProcessList 中查找
  if (typeof process.minorProcess === 'string' && process.majorProcess) {
    // 檢查 minorProcessList 是否存在及是否有該 majorProcess 的項目
    if (minorProcessListRef.value && minorProcessListRef.value[process.majorProcess]) {
      const minorItems = minorProcessListRef.value[process.majorProcess];
      const found = minorItems.find(item => item.code === process.minorProcess);
      if (found) return found.name;
    }
    // 如果找不到，則直接返回 minorProcess 字串
    return process.minorProcess;
  }

  // 最後的備用選項，返回空字串
  return '';
};







// 添加工序選單相關變數
const processMenuVisible = ref(false);
const processMenuStyle = ref({});
const currentProcess = ref(null);

// 材料選單相關
const materialMenuVisible = ref(false);
const materialMenuStyle = ref({});
const currentMaterial = ref(null);
const filteredMaterial = ref([]);

// 使用從 utils 導入的 showProcessMenu 函數
// 包裝函數，以便傳遞正確的參數
const showProcessMenuWrapper = (process, event) => {
  // 阻止事件冒泡
  event.stopPropagation();

  // 使用從 utils 導入的 showProcessMenu 函數
  const result = showProcessMenu(process, event);

  // 更新選單樣式和當前工序
  processMenuStyle.value = result.processMenuStyle;
  currentProcess.value = result.currentProcess;

  // 顯示選單
  processMenuVisible.value = true;

  // 點擊其他區域時關閉選單
  const closeMenu = (e) => {
    if (!e.target.closest('.process-menu') && !e.target.closest('.process-item')) {
      processMenuVisible.value = false;
      document.removeEventListener('click', closeMenu);
    }
  };

  setTimeout(() => {
    document.addEventListener('click', closeMenu);
  }, 100);
};

// 使用從 utils 導入的 addNextProcess 函數
// 包裝函數，以便傳遞正確的參數
const addNextProcessWrapper = () => {
  if (!currentProcess.value) return;

  // 工序唯一性檢查：每個工序只能產生一個下一階段工序
  const hasNextProcess = savedProcesses.value.some(p => p.sourceProcessId === currentProcess.value.id);
  if (hasNextProcess) {
    showError('此工序已經產生過下一階段工序，無法重複創建。');
    return;
  }

  // 關閉選單
  processMenuVisible.value = false;

  // 使用從 utils 導入的 addNextProcess 函數
  const result = addNextProcess(
    currentProcess.value,
    processForm.value,
    partStructureListRef.value,
    pieceStructureListRef.value
  );

  // 更新選擇的部位和表單
  selectedPosition.value = result.selectedPosition;

  // 清空選擇的工序 ID，表示新增模式
  selectedProcessId.value = null;

  // 自動填充材料區域已選擇的組織信息
  fillMaterialStructureInfo();

  // 打開模態對話框
  processModalVisible.value = true;
};

// 編輯當前工序
const editCurrentProcess = () => {
  if (!currentProcess.value) return;

  // 關閉選單
  processMenuVisible.value = false;

  // 設置當前工序的信息到表單
  const process = currentProcess.value;
  processForm.value = {
    pieceName: process.pieceName || '',
    partStructure: process.partStructure || '',
    pieceStructure: process.pieceStructure || '',
    material: process.material || '',
    majorProcess: process.majorProcess || '',
    minorProcess: process.minorProcess || '',
    tool: process.tool || '',
    consumable: process.consumable || '',
    quantity: process.quantity || 1
  };

  // 更新選擇的部位和工序ID
  selectedPosition.value = process.pieceName;
  selectedProcessId.value = process.id;

  // 打開模態對話框
  processModalVisible.value = true;
};

// 使用從 utils 導入的 toggleFixProcess 函數
// 包裝函數，以便傳遞正確的參數
const toggleFixProcessWrapper = async () => {
  if (!currentProcess.value) return;

  // 關閉選單
  processMenuVisible.value = false;

  // 儲存操作前的固定狀態
  const wasFixed = currentProcess.value.isFixed;
  const processName = currentProcess.value.pieceName;

  // 使用從 utils 導入的 toggleFixProcess 函數
  toggleFixProcess(
    currentProcess.value,
    savedProcesses.value,
    bomId.value,
    null, // 連接線功能已移除
    null // 連接線功能已移除
  );
  
  // 同步到後端
  syncProcessesToBackend(savedProcesses.value, bomId.value, api).catch(error => {
    console.error('同步工序到後端失敗:', error);
  });

  // 保存當前狀態到歷史記錄
  const action = wasFixed ? '取消固定' : '固定';
  await saveCurrentStateToHistory(`${action}工序：${processName}`);
};





// 判斷材料是否已被使用
const isMaterialUsed = (materialName) => {
  if (!savedProcesses.value || !Array.isArray(savedProcesses.value)) return false;
  return savedProcesses.value.some(p => p.pieceName === materialName);
};

// 判斷工序是否已被使用（已產生下一階段工序）
const isProcessUsed = (process) => {
  if (!process || !savedProcesses.value || !Array.isArray(savedProcesses.value)) return false;
  return savedProcesses.value.some(p => p.sourceProcessId === process.id);
};

// 判斷材料是否已被合併（材料的工序被其他工序合併）
const isMaterialMerged = (materialName) => {
  if (!savedProcesses.value || !Array.isArray(savedProcesses.value)) return false;

  // 檢查是否有任何工序被合併到其他工序中
  return savedProcesses.value.some(process => {
    if (!process.originalSources) return false;

    return process.originalSources.some(source => {
      if (source.mergedProcessId) {
        const mergedProcess = savedProcesses.value.find(p => p.id === source.mergedProcessId);
        return mergedProcess && mergedProcess.pieceName === materialName;
      }
      return false;
    });
  });
};

// 計算屬性：處理材料分組（包括合併後的材料）
const groupedMaterials = computed(() => {
  const groups = [];
  const processedMaterials = new Set();

  if (!savedProcesses.value || !Array.isArray(savedProcesses.value) || !positionNames.value) {
    return [];
  }

  // 首先處理合併後的材料
  savedProcesses.value.forEach(process => {
    if (process.originalSources && process.originalSources.length > 0) {
      const lastSource = process.originalSources[process.originalSources.length - 1];
      if (lastSource.originalCurrentMaterialName && lastSource.originalMergeMaterialName) {
        const mergedMaterialName = process.pieceName;

        if (!processedMaterials.has(mergedMaterialName)) {
          groups.push({
            displayName: mergedMaterialName,
            type: 'merged',
            originalMaterials: [lastSource.originalCurrentMaterialName, lastSource.originalMergeMaterialName],
            collapsed: true
          });

          // 標記原始材料為已處理
          processedMaterials.add(lastSource.originalCurrentMaterialName);
          processedMaterials.add(lastSource.originalMergeMaterialName);
          processedMaterials.add(mergedMaterialName);
        }
      }
    }
  });

  // 然後處理未合併的原始材料
  positionNames.value.forEach(materialName => {
    if (!processedMaterials.has(materialName)) {
      groups.push({
        displayName: materialName,
        type: 'original',
        originalMaterials: [materialName],
        collapsed: false
      });
    }
  });

  return groups;
});

// 材料組的摺疊狀態
const materialGroupCollapsed = ref({});

// 處理材料點擊
const handleMaterialClick = (item, event) => {
  // 阻止事件冒泡
  event.stopPropagation();

  // 多選模式下，直接選取材料
  if (isMultiSelectMode.value) {
    handleMultiSelectMaterial(item, event);
    return;
  }

  // 設置當前材料
  currentMaterial.value = item;

  // 計算選單位置
  const rect = event.target.getBoundingClientRect();
  materialMenuStyle.value = {
    top: `${rect.top}px`,
    left: `${rect.right + 5}px`
  };

  // 顯示選單
  materialMenuVisible.value = true;

  // 點擊其他區域時關閉選單
  const closeMenu = (e) => {
    if (!e.target.closest('.material-menu') && !e.target.closest('.position-item') && !e.target.closest('.material-group-header')) {
      materialMenuVisible.value = false;
      document.removeEventListener('click', closeMenu);
    }
  };

  setTimeout(() => {
    document.addEventListener('click', closeMenu);
  }, 100);
};

// 處理合併材料點擊
const handleMergedMaterialClick = (mergedMaterialName, event) => {
  // 阻止事件冒泡
  event.stopPropagation();

  // 多選模式下不處理合併材料
  if (isMultiSelectMode.value) {
    return;
  }

  // 設置當前材料為合併後的材料名稱
  currentMaterial.value = mergedMaterialName;

  // 計算選單位置
  const rect = event.target.getBoundingClientRect();
  materialMenuStyle.value = {
    top: `${rect.top}px`,
    left: `${rect.right + 5}px`
  };

  // 顯示選單
  materialMenuVisible.value = true;

  // 點擊其他區域時關閉選單
  const closeMenu = (e) => {
    if (!e.target.closest('.material-menu') && !e.target.closest('.material-group-header')) {
      materialMenuVisible.value = false;
      document.removeEventListener('click', closeMenu);
    }
  };

  setTimeout(() => {
    document.addEventListener('click', closeMenu);
  }, 100);
};

// 材料選單 - 新增工序
const addProcessForMaterial = () => {
  if (!currentMaterial.value) return;

  // 關閉選單
  materialMenuVisible.value = false;

  // 檢查材料是否已被使用
  if (isMaterialUsed(currentMaterial.value)) {
    showWarning('此材料已被使用，無法重複新增工序');
    return;
  }

  // 開啟工序選擇對話框
  toggleSelectPosition(currentMaterial.value);
};

// 顯示工序（材料篩選）
const showProcessesForMaterial = () => {
  if (!currentMaterial.value) return;
  materialMenuVisible.value = false;
  const relatedProcesses = savedProcesses.value.filter(p => p.pieceName === currentMaterial.value);
  if (relatedProcesses.length === 0) {
    showWarning('此材料尚未建立任何工序');
    return;
  }
  if (!filteredMaterial.value.includes(currentMaterial.value)) {
    filteredMaterial.value.push(currentMaterial.value);
    localStorage.setItem('processFilteredMaterial', JSON.stringify(filteredMaterial.value));
    // 新增：高亮該材料
    hoveredMaterial.value = currentMaterial.value;
    // notification.success(`已加入「${currentMaterial.value}」到工序篩選`); // 不再顯示成功提示
  } else {
    // 新增：高亮該材料
    hoveredMaterial.value = currentMaterial.value;
    // notification.success(`「${currentMaterial.value}」已在工序篩選中`); // 不再顯示成功提示
  }
};

// 清除材料篩選
const clearMaterialFilter = () => {
  filteredMaterial.value = [];
  localStorage.removeItem('processFilteredMaterial');
  // notification.success('已清除所有材料工序篩選'); // 不再顯示成功提示
};

const removeMaterialFromFilter = (material) => {
  filteredMaterial.value = filteredMaterial.value.filter(m => m !== material);
  if (filteredMaterial.value.length > 0) {
    localStorage.setItem('processFilteredMaterial', JSON.stringify(filteredMaterial.value));
  } else {
    localStorage.removeItem('processFilteredMaterial');
  }
  // notification.success(`已移除「${material}」的工序篩選`); // 不再顯示成功提示
};

// 頁面加載時初始化
onMounted(async () => {
  // 獲取當前用戶信息
  await fetchCurrentUser();

  if (!bomId.value) {
    // 如果沒有指定bomId，嘗試從localStorage獲取
    const storedBomId = localStorage.getItem('currentBomId');
    if (storedBomId) {
      bomId.value = storedBomId;
    } else {
      // 如果仍然沒有bomId，返回到BOM頁面
      showWarning('無法獲取BOM ID，請重新選擇BOM');
      router.push({ name: 'design-bom' });
      return;
    }
  }

  // 獲取BOM信息，檢查確認狀態
  await fetchBomInfo();

  // 初始化歷史記錄 (從後端載入)
  await historyManager.initialize();

  // 獲取部位名稱
  await fetchPositionNames();

  // 從後端或 localStorage 加載工序列表
  await loadProcessesWrapper();

  // 恢復材料篩選（多選）
  const lastMaterial = localStorage.getItem('processFilteredMaterial');
  if (lastMaterial) {
    try {
      const arr = JSON.parse(lastMaterial);
      if (Array.isArray(arr)) filteredMaterial.value = arr;
    } catch {}
  }

  // 只有在沒有歷史記錄時，才保存初始狀態
  const stats = historyManager.getStats();
  if (stats.totalCount === 0) {
    await saveCurrentStateToHistory('初始狀態');
  }

  // 監聽視窗大小變化
  window.addEventListener('resize', updateConnectionLines);

  // 連接線功能已移除
});

// 連接線相關
const connectionSvgWidth = ref(1200);
const connectionSvgHeight = ref(600);

// 計算是否有固定工序
const hasFixedProcesses = computed(() => {
  return savedProcesses.value.some(p => p.isFixed);
});

// 動態計算所有需要顯示的連接線（固定工序或滑鼠懸停工序）
const processConnections = computed(() => {
  const connections = [];
  // 固定工序的連接線
  const fixedProcesses = savedProcesses.value.filter(p => p.isFixed);
  fixedProcesses.forEach(fixedProcess => {
    connections.push(...getProcessConnectionsFor(fixedProcess));
  });
  // 滑鼠懸停工序的連接線
  if (hoveredProcess.value) {
    const hovered = savedProcesses.value.find(p => p.id === hoveredProcess.value);
    if (hovered) {
      connections.push(...getProcessConnectionsFor(hovered));
    }
  }
  return connections;
});

// 取得某個工序的前後連接線
function getProcessConnectionsFor(process) {
  const result = [];
  const allMaterialProcesses = savedProcesses.value.filter(p => p.pieceName === process.pieceName);
  const sorted = allMaterialProcesses.sort((a, b) => {
    const aIndex = getProcessIndex(a, a.majorProcess);
    const bIndex = getProcessIndex(b, b.majorProcess);
    return parseInt(aIndex) - parseInt(bIndex);
  });
  const idx = sorted.findIndex(p => p.id === process.id);
  if (idx > 0) {
    const fromProcess = sorted[idx - 1];
    const toProcess = sorted[idx];
    const fromEl = document.querySelector(`[data-id="${fromProcess.id}"]`);
    const toEl = document.querySelector(`[data-id="${toProcess.id}"]`);
    if (fromEl && toEl) {
      const fromRect = fromEl.getBoundingClientRect();
      const toRect = toEl.getBoundingClientRect();
      const containerRect = document.querySelector('.process-flow-content')?.getBoundingClientRect();
      if (containerRect) {
        const x1 = fromRect.left - containerRect.left;
        const y1 = fromRect.top - containerRect.top + fromRect.height / 2;
        const x2 = toRect.right - containerRect.left;
        const y2 = toRect.top - containerRect.top + toRect.height / 2;
        const offsetX = 5;
        const pathData = calculateCurvedPath(x1 + offsetX, y1, x2 - offsetX, y2, 0);
        result.push({
          x1: x1 + offsetX,
          y1: y1,
          x2: x2 - offsetX,
          y2: y2,
          pathData: pathData,
          fromProcess: fromProcess,
          toProcess: toProcess,
          isCurved: true
        });
      }
    }
  }
  if (idx < sorted.length - 1 && idx !== -1) {
    const fromProcess = sorted[idx];
    const toProcess = sorted[idx + 1];
    const fromEl = document.querySelector(`[data-id="${fromProcess.id}"]`);
    const toEl = document.querySelector(`[data-id="${toProcess.id}"]`);
    if (fromEl && toEl) {
      const fromRect = fromEl.getBoundingClientRect();
      const toRect = toEl.getBoundingClientRect();
      const containerRect = document.querySelector('.process-flow-content')?.getBoundingClientRect();
      if (containerRect) {
        const x1 = fromRect.left - containerRect.left;
        const y1 = fromRect.top - containerRect.top + fromRect.height / 2;
        const x2 = toRect.right - containerRect.left;
        const y2 = toRect.top - containerRect.top + toRect.height / 2;
        const offsetX = 5;
        const pathData = calculateCurvedPath(x1 + offsetX, y1, x2 - offsetX, y2, 1);
        result.push({
          x1: x1 + offsetX,
          y1: y1,
          x2: x2 - offsetX,
          y2: y2,
          pathData: pathData,
          fromProcess: fromProcess,
          toProcess: toProcess,
          isCurved: true
        });
      }
    }
  }
  return result;
}

// 計算彎曲路徑以避免重疊
const calculateCurvedPath = (x1, y1, x2, y2, index) => {
  const distance = x2 - x1;
  const midX = (x1 + x2) / 2;
  
  // 如果距離太短，使用直線
  if (distance < 50) {
    return `M ${x1} ${y1} L ${x2} ${y2}`;
  }
  
  // 彎曲程度
  const curveOffset = (Math.floor(index / 2) + 1) * 70;
  // 統一都往下彎曲
  const controlY = y1 + curveOffset;
  
  // 創建彎曲路徑，使用更平滑的曲線
  return `M ${x1} ${y1} Q ${midX} ${controlY} ${x2} ${y2}`;
};

// 更新連接線位置
const updateConnectionLines = () => {
  nextTick(() => {
    // 觸發重新計算
    connectionSvgWidth.value = document.querySelector('.process-flow-content')?.offsetWidth || 1200;
    connectionSvgHeight.value = document.querySelector('.process-flow-content')?.offsetHeight || 600;
  });
};

// 監聽工序變化，更新連接線
watch(savedProcesses, () => {
  updateConnectionLines();
}, { deep: true });

// 監聽視窗大小變化已合併到主要的onMounted中

onUnmounted(() => {
  window.removeEventListener('resize', updateConnectionLines);
});

// 多選模式狀態
const isMultiSelectMode = ref(false);
const multiSelectedMaterials = ref([]); // 改為選取材料而不是工序
const multiSelectedProcesses = ref([]); // 新增：選取工序

// 切換多選模式
const toggleMultiSelectMode = () => {
  isMultiSelectMode.value = !isMultiSelectMode.value;
  if (isMultiSelectMode.value) {
    // 進入多選模式時自動展開材料區
    materialCollapsed.value = false;
  } else {
    // 退出多選模式時清空選擇
    multiSelectedMaterials.value = [];
    multiSelectedProcesses.value = [];
  }
};

// 處理材料多選
const handleMultiSelectMaterial = (material, event) => {
  const idx = multiSelectedMaterials.value.indexOf(material);
  if (idx === -1) {
    multiSelectedMaterials.value.push(material);
  } else {
    multiSelectedMaterials.value.splice(idx, 1);
  }
};

// 處理工序多選
const handleMultiSelectProcess = (processId, event) => {
  const idx = multiSelectedProcesses.value.indexOf(processId);
  if (idx === -1) {
    multiSelectedProcesses.value.push(processId);
  } else {
    multiSelectedProcesses.value.splice(idx, 1);
  }
};

// 開啟批量刪除工序對話框
const openBatchDeleteModal = async () => {
  if (multiSelectedProcesses.value.length === 0) return;
  
  if (confirm(`確定要刪除選取的 ${multiSelectedProcesses.value.length} 項工序嗎？此操作無法撤銷。`)) {
    // 執行批量刪除
    const deletedCount = multiSelectedProcesses.value.length;
    
    // 獲取被刪除工序的名稱用於歷史記錄
    const deletedProcessNames = savedProcesses.value
      .filter(process => multiSelectedProcesses.value.includes(process.id))
      .map(process => process.pieceName)
      .join('、');
    
    // 從工序列表中移除選取的工序
    savedProcesses.value = savedProcesses.value.filter(process => 
      !multiSelectedProcesses.value.includes(process.id)
    );
    
    // 保存到 localStorage
    saveProcessesToLocalStorage(bomId.value, savedProcesses.value);
    
    // 同步到後端
    syncProcessesToBackend(savedProcesses.value, bomId.value, api).catch(error => {
      console.error('同步工序到後端失敗:', error);
    });

    // 保存當前狀態到歷史記錄
    await saveCurrentStateToHistory(`批量刪除 ${deletedCount} 項工序：${deletedProcessNames}`);
    
    // 清空選擇並退出多選模式
    multiSelectedProcesses.value = [];
    multiSelectedMaterials.value = [];
    isMultiSelectMode.value = false;
    
    showSuccess(`已批量刪除 ${deletedCount} 項工序`);
  }
};



function openBatchAddModal() {
  if (multiSelectedMaterials.value.length === 0) return;
  
  // 開啟批量新增工序對話框
  batchProcessModalVisible.value = true;
  batchProcessForm.value = {
    pieceName: '', // 將在模板中自動填入第一個材料名稱
    partStructure: '',
    pieceStructure: '',
    material: '',
    majorProcess: '',
    minorProcess: '',
    tool: '',
    consumable: '',
    quantity: 1
  };
}

// 批量保存工序
const saveBatchProcess = async () => {
  if (!batchProcessForm.value.majorProcess || !batchProcessForm.value.minorProcess) {
    showError('請選擇大工序和小工序');
    return;
  }
  const newProcesses = [];
  const skippedMaterials = [];
  multiSelectedMaterials.value.forEach((material, index) => {
    if (isMaterialUsed(material)) {
      skippedMaterials.push(material);
      return;
    }
    const newProcess = {
      id: `temp_${Date.now()}_${Math.floor(Math.random() * 1000)}_${index}`,
      pieceName: batchPieceNames.value[index] || material,
      majorProcess: batchProcessForm.value.majorProcess,
      minorProcess: batchProcessForm.value.minorProcess ? {
        code: batchProcessForm.value.minorProcess.code,
        name: batchProcessForm.value.minorProcess.name
      } : null,
      sequenceNumber: '',
      sourcePosition: material,
      tool: batchProcessForm.value.tool,
      consumable: batchProcessForm.value.consumable,
      quantity: batchProcessForm.value.quantity,
      sourceProcess: null,
      sourceProcessId: null,
      isCompleted: false,
      partStructure: batchProcessForm.value.partStructure ? batchProcessForm.value.partStructure.code : null,
      pieceStructure: batchProcessForm.value.pieceStructure ? batchProcessForm.value.pieceStructure.code : null,
      material: batchProcessForm.value.material || null,
      bomId: bomId.value
    };
    newProcesses.push(newProcess);
  });
  if (newProcesses.length === 0) {
    showError('所有選取的材料都已被使用，無法新增任何工序');
    return;
  }
  if (skippedMaterials.length > 0) {
    showWarning('以下材料已被使用，未新增工序：' + skippedMaterials.join('、'));
  }
  savedProcesses.value.push(...newProcesses);
  saveProcessesToLocalStorage(bomId.value, savedProcesses.value);
  syncProcessesToBackend(savedProcesses.value, bomId.value, api).catch(error => {
    console.error('同步工序到後端失敗:', error);
  });

  // 保存當前狀態到歷史記錄
      await saveCurrentStateToHistory(`批量新增 ${newProcesses.length} 項工序`);

  batchProcessModalVisible.value = false;
  isMultiSelectMode.value = false;
  multiSelectedMaterials.value = [];
  showSuccess(`已批量為 ${newProcesses.length} 項材料新增工序`);
};

// 關閉批量新增對話框
const closeBatchProcessModal = () => {
  batchProcessModalVisible.value = false;
  batchProcessForm.value = {
    pieceName: '',
    partStructure: '',
    pieceStructure: '',
    material: '',
    majorProcess: '',
    minorProcess: '',
    tool: '',
    consumable: '',
    quantity: 1
  };
};

// 批量新增對話框的部位組織變更處理
const handleBatchPartStructureChange = () => {
  // 清空分片組織選擇
  batchProcessForm.value.pieceStructure = '';
};

// 批量新增對話框的大工序變更處理
const handleBatchMajorProcessChange = () => {
  // 清空小工序選擇
  batchProcessForm.value.minorProcess = '';
};

const batchPieceNames = ref([]);

watch(batchProcessModalVisible, (val) => {
  if (val) {
    // 對每個選取的材料初始化分片名稱
    batchPieceNames.value = multiSelectedMaterials.value.map(name => name);
  }
});

</script>

<style scoped>
@import './ProcessPreparationView.css';

/* 添加工序選單樣式 */
.process-menu {
  position: fixed;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  width: 120px;
}

.process-menu-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
}

.process-menu-item:hover {
  background-color: #f3f4f6;
}

.process-menu-item.disabled {
  pointer-events: none;
  background: #eee;
  color: #bbb;
  cursor: not-allowed;
}

.process-menu-item.disabled:hover {
  background: #eee;
}

.process-menu-item i {
  margin-right: 8px;
  color: #475569;
}

.process-menu-item.disabled i {
  color: #bbb;
}

/* 材料選單樣式 */
.material-menu {
  position: fixed;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  width: 120px;
}

.material-menu-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
}

.material-menu-item:hover {
  background-color: #f3f4f6;
}

.material-menu-item i {
  margin-right: 8px;
  color: #475569;
}

.material-menu-item.disabled {
  pointer-events: none;
  background: #eee;
  color: #bbb;
  cursor: not-allowed;
}



/* 合併工序信息樣式 */
.process-item-merged-info {
  font-size: 0.75em;
  color: #6b7280;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 材料合併狀態樣式 */
.position-item.merged {
  opacity: 0.5;
  background-color: #e5e7eb !important;
  color: #6b7280 !important;
}

/* 合併工序按鈕樣式 */
.merge-btn {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 8px;
}

.merge-btn:hover {
  background-color: #2563eb;
}

.unmerge-btn {
  background-color: #f59e0b;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 8px;
}

.unmerge-btn:hover {
  background-color: #d97706;
}

/* 合併工序選擇對話框樣式 */
.merge-process-list {
  max-height: 400px;
  overflow-y: auto;
}

.merge-instruction {
  margin-bottom: 16px;
  color: #374151;
  font-weight: 500;
}

.process-selection-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.process-selection-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.process-selection-item:hover {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

.process-selection-item.selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.process-info {
  flex: 1;
}

.process-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.process-details {
  font-size: 14px;
  color: #6b7280;
}

.process-status {
  margin-left: 12px;
}

.no-processes {
  text-align: center;
  color: #6b7280;
  font-style: italic;
  padding: 20px;
  white-space: nowrap;
}

/* 合併分片名稱輸入樣式 */
.merge-name-input {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.merge-name-input label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
}

.merge-piece-name-input {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.merge-piece-name-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.merge-name-preview {
  margin-top: 16px;
  padding: 16px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.preview-title {
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.preview-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.original-names {
  display: flex;
  align-items: center;
  gap: 12px;
}

.original-name {
  padding: 8px 12px;
  background-color: #e5e7eb;
  border-radius: 6px;
  font-weight: 500;
  color: #374151;
}

.plus-sign {
  font-weight: bold;
  color: #6b7280;
  font-size: 18px;
}

.arrow-down {
  color: #3b82f6;
  font-size: 16px;
}

.merged-name {
  padding: 10px 16px;
  background-color: #3b82f6;
  color: white;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
}

/* 材料組樣式 */
.material-group {
  margin-bottom: 4px;
}

.merged-group {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  overflow: hidden;
}

.material-group-header {
  background-color: #f3f4f6;
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.2s;
}

.material-group-header:hover {
  background-color: #e5e7eb;
}

.material-group-header.highlighted {
  background-color: #dbeafe;
  border-color: #3b82f6;
}

.group-name {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #374151;
}

.collapse-icon {
  margin-right: 8px;
  font-size: 12px;
  transition: transform 0.2s;
}

.merged-indicator {
  margin-left: 8px;
  font-size: 12px;
  color: #3b82f6;
  font-weight: 500;
}

.original-materials-list {
  background-color: #fafafa;
}

.original-material {
  margin: 0 !important;
  border-bottom: 1px solid #e5e7eb;
  opacity: 0.7;
  padding-left: 24px !important;
}

.original-material:last-child {
  border-bottom: none;
}

.material-merged-indicator {
  margin-left: auto;
  font-size: 11px;
  color: #6b7280;
  font-style: italic;
}

.material-has-process {
  margin-top: 2px;
}

.position-item.disabled {
  pointer-events: none;
  background: #eee;
  color: #bbb;
  cursor: not-allowed;
}

.material-filter-bar {
  background: #f0f9ff;
  color: #0369a1;
  border: 1px solid #7dd3fc;
  border-radius: 4px;
  padding: 8px 16px;
  margin: 12px 0 0 0;
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 15px;
}
.clear-filter-btn {
  background: #e0e7ef;
  color: #475569;
  border: none;
  border-radius: 4px;
  padding: 4px 12px;
  cursor: pointer;
  font-size: 14px;
  margin-left: 8px;
  transition: background 0.2s;
}
.clear-filter-btn:hover {
  background: #bae6fd;
}
.remove-filter-btn {
  background: transparent;
  color: #e11d48;
  border: none;
  font-size: 16px;
  margin: 0 2px;
  cursor: pointer;
  vertical-align: middle;
  padding: 0 2px;
  line-height: 1;
}
.remove-filter-btn:hover {
  color: #be123c;
}
.material-collapse-header {
  cursor: pointer;
  padding: 6px 0 6px 8px;
  font-weight: bold;
  display: flex;
  align-items: center;
  user-select: none;
}
.fade-enter-active, .fade-leave-active {
  transition: all 0.3s;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
  max-height: 0;
}
.fade-enter-to, .fade-leave-from {
  opacity: 1;
  max-height: 500px;
}
.process-item.highlighted {
  box-shadow: 0 0 0 2px #2563eb;
  opacity: 1 !important;
}
.process-item.dimmed {
  opacity: 0.3;
}



/* 批量新增對話框樣式 */
.batch-info {
  background: #f0f9ff;
  border: 1px solid #7dd3fc;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.info-icon {
  font-size: 16px;
  margin-top: 2px;
}

.info-text {
  font-size: 14px;
  color: #0369a1;
  line-height: 1.4;
}

.selected-materials-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 8px;
  background: #f9fafb;
}

.selected-material-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  margin: 2px 0;
  background: white;
  border-radius: 3px;
  border: 1px solid #e5e7eb;
}

.material-name {
  font-weight: 500;
  color: #374151;
}

.material-used {
  color: #ef4444;
  font-size: 0.875em;
  font-style: italic;
}

.piece-name-preview {
  max-height: 150px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 8px;
  background: #f0f9ff;
}

.preview-item {
  padding: 4px 8px;
  margin: 2px 0;
  background: white;
  border-radius: 3px;
  border: 1px solid #dbeafe;
  color: #1e40af;
  font-weight: 500;
}

.help-text {
  font-size: 0.875em;
  color: #6b7280;
  margin-top: 4px;
  font-style: italic;
}

.connection-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 99;
}

.connection-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.connection-line {
  transition: all 0.3s ease;
  opacity: 0.7;
  stroke: #b0b0b0;
}

.process-item {
  position: relative;
  z-index: 1;
}

/* 新增樣式 */
.material-label {
  display: inline-block;
  min-width: 80px;
  color: #64748b;
  font-size: 14px;
  margin-right: 4px;
}

.batch-add-btn {
  background: #10b981;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 8px;
  transition: background 0.2s;
}

.batch-delete-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 8px;
  transition: background 0.2s;
}

.batch-delete-btn:hover {
  background: #dc2626;
}

.batch-delete-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.process-item.multi-selecting {
  cursor: pointer;
  border: 2px solid #e5e7eb;
}

.process-item.multi-selected {
  border: 2px solid #3b82f6;
  background-color: #eff6ff;
}
</style>